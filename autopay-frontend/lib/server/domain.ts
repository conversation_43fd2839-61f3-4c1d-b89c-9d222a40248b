import { headers } from 'next/headers';
import type { DomainConfig } from '../types/domain';

/**
 * Get domain configuration on the server side
 */
export async function getDomainConfig(): Promise<DomainConfig | null> {
  try {
    const headersList = await headers();

    // Try to get domain config from middleware headers first
    const domainConfigHeader = headersList.get('x-domain-config');
    if (domainConfigHeader) {
      return JSON.parse(domainConfigHeader);
    }

    // Fallback: fetch from API
    const hostname = headersList.get('host') || 'localhost';
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/domain/config?hostname=${hostname}`,
      {
        headers: {
          Accept: 'application/json',
        },
        // Add cache control for server-side requests
        next: { revalidate: 300 }, // Cache for 5 minutes
      },
    );

    if (!response.ok) {
      console.warn(`Failed to fetch domain config: ${response.statusText}`);
      return null;
    }

    const data = await response.json();

    if (data.success) {
      return data.data;
    } else {
      console.warn('Domain config fetch failed:', data.message);
      return null;
    }
  } catch (error) {
    console.error('Error fetching domain config:', error);
    return null;
  }
}

/**
 * Get hostname from headers
 */
export async function getHostname(): Promise<string> {
  const headersList = await headers();
  return headersList.get('host') || 'localhost';
}
