export interface DomainConfig {
  id: string
  hostname: string
  name: string
  description?: string
  branding: {
    name: string
    slogan?: string
    logo_url?: string
    favicon_url?: string
  }
  theme: {
    primary: string
    secondary: string
    accent: string
    background: string
    surface: string
    text: string
    text_secondary: string
  }
  seo: {
    title: string
    description?: string
    keywords?: string
    og_image?: string
  }
  features: string[]
  feature_configs: Record<string, any>
  locale: {
    default: string
    supported: string[]
  }
  contact: Record<string, any>
  custom: Record<string, any>
  font_family?: string
  custom_css: Record<string, any>
}

export interface DomainContextType {
  config: DomainConfig | null
  isLoading: boolean
  error: string | null
  refreshConfig: () => Promise<void>
  hasFeature: (feature: string) => boolean
  getFeatureConfig: (feature: string) => any
}
