'use client';

import React, { createContext, useCallback, useEffect, useState } from 'react';
import type { DomainConfig, DomainContextType } from '../types/domain';

export const DomainContext = createContext<DomainContextType | null>(null);

interface DomainProviderProps {
  children: React.ReactNode;
  initialConfig?: DomainConfig | null;
}

export function DomainProvider({
  children,
  initialConfig,
}: DomainProviderProps): JSX.Element {
  const [config, setConfig] = useState<DomainConfig | null>(
    initialConfig || null,
  );
  const [isLoading, setIsLoading] = useState(!initialConfig);
  const [error, setError] = useState<string | null>(null);

  const fetchDomainConfig = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      const hostname = window.location.hostname;
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/domain/config?hostname=${hostname}`,
        {
          headers: {
            Accept: 'application/json',
          },
        },
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch domain config: ${response.statusText}`,
        );
      }

      const data = await response.json();

      if (data.success) {
        setConfig(data.data);
      } else {
        throw new Error(data.message || 'Failed to fetch domain config');
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching domain config:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshConfig = useCallback(async (): Promise<void> => {
    await fetchDomainConfig();
  }, [fetchDomainConfig]);

  const hasFeature = useCallback(
    (feature: string): boolean => {
      return config?.features?.includes(feature) ?? false;
    },
    [config],
  );

  const getFeatureConfig = useCallback(
    (feature: string): any => {
      return config?.feature_configs?.[feature] ?? {};
    },
    [config],
  );

  useEffect(() => {
    if (!initialConfig) {
      fetchDomainConfig();
    }
  }, [initialConfig, fetchDomainConfig]);

  // Apply theme CSS variables when config changes
  useEffect(() => {
    if (config?.theme) {
      const root = document.documentElement;

      // Apply theme colors as CSS custom properties
      Object.entries(config.theme).forEach(([key, value]) => {
        root.style.setProperty(`--domain-${key}`, value);
      });

      // Apply custom font family
      if (config.font_family) {
        root.style.setProperty('--domain-font-family', config.font_family);
      }

      // Apply custom CSS
      if (config.custom_css && Object.keys(config.custom_css).length > 0) {
        const styleId = 'domain-custom-styles';
        let styleElement = document.getElementById(styleId) as HTMLStyleElement;

        if (!styleElement) {
          styleElement = document.createElement('style');
          styleElement.id = styleId;
          document.head.appendChild(styleElement);
        }

        // Convert custom CSS object to CSS string
        const cssString = Object.entries(config.custom_css)
          .map(([selector, styles]) => {
            if (typeof styles === 'object') {
              const styleProps = Object.entries(
                styles as Record<string, string>,
              )
                .map(([prop, value]) => `${prop}: ${value};`)
                .join(' ');
              return `${selector} { ${styleProps} }`;
            }
            return '';
          })
          .join('\n');

        styleElement.textContent = cssString;
      }
    }
  }, [config]);

  // Update document title and meta tags
  useEffect(() => {
    if (config?.seo) {
      if (config.seo.title) {
        document.title = config.seo.title;
      }

      // Update meta description
      if (config.seo.description) {
        let metaDescription = document.querySelector(
          'meta[name="description"]',
        ) as HTMLMetaElement;
        if (!metaDescription) {
          metaDescription = document.createElement('meta');
          metaDescription.name = 'description';
          document.head.appendChild(metaDescription);
        }
        metaDescription.content = config.seo.description;
      }

      // Update meta keywords
      if (config.seo.keywords) {
        let metaKeywords = document.querySelector(
          'meta[name="keywords"]',
        ) as HTMLMetaElement;
        if (!metaKeywords) {
          metaKeywords = document.createElement('meta');
          metaKeywords.name = 'keywords';
          document.head.appendChild(metaKeywords);
        }
        metaKeywords.content = config.seo.keywords;
      }

      // Update Open Graph image
      if (config.seo.og_image) {
        let ogImage = document.querySelector(
          'meta[property="og:image"]',
        ) as HTMLMetaElement;
        if (!ogImage) {
          ogImage = document.createElement('meta');
          ogImage.setAttribute('property', 'og:image');
          document.head.appendChild(ogImage);
        }
        ogImage.content = config.seo.og_image;
      }
    }
  }, [config]);

  // Update favicon
  useEffect(() => {
    if (config?.branding?.favicon_url) {
      let favicon = document.querySelector(
        'link[rel="icon"]',
      ) as HTMLLinkElement;
      if (!favicon) {
        favicon = document.createElement('link');
        favicon.rel = 'icon';
        document.head.appendChild(favicon);
      }
      favicon.href = config.branding.favicon_url;
    }
  }, [config]);

  const contextValue: DomainContextType = {
    config,
    isLoading,
    error,
    refreshConfig,
    hasFeature,
    getFeatureConfig,
  };

  return (
    <DomainContext.Provider value={contextValue}>
      {children}
    </DomainContext.Provider>
  );
}
