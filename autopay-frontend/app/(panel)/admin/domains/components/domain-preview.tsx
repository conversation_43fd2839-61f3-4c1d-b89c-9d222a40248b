'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { DomainLogo } from '@/components/domain/domain-logo'
import { Eye, Globe, Palette, Settings, X } from 'lucide-react'
import { DomainConfig } from '@/lib/types/domain'

interface DomainPreviewProps {
  domain: DomainConfig
  onClose: () => void
}

export function DomainPreview({ domain, onClose }: DomainPreviewProps) {
  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Xem trước Domain: {domain.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Preview Header */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Thông tin cơ bản
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Hostname</h4>
                  <p className="font-mono">{domain.hostname}</p>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Tên hiển thị</h4>
                  <p>{domain.name}</p>
                </div>
              </div>
              {domain.description && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Mô tả</h4>
                  <p>{domain.description}</p>
                </div>
              )}
              <div className="flex gap-2">
                <Badge variant={domain.custom?.is_active ? "secondary" : "destructive"}>
                  {domain.custom?.is_active ? "Hoạt động" : "Tạm dừng"}
                </Badge>
                {domain.custom?.is_default && (
                  <Badge variant="default">Mặc định</Badge>
                )}
                {domain.custom?.force_https && (
                  <Badge variant="outline">HTTPS</Badge>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Branding Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Thương hiệu
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4 p-4 border rounded-lg bg-muted/50">
                {domain.branding?.logo_url ? (
                  <img 
                    src={domain.branding.logo_url} 
                    alt={domain.branding.name}
                    className="h-12 w-12 object-contain"
                  />
                ) : (
                  <div className="h-12 w-12 bg-primary rounded text-primary-foreground text-lg flex items-center justify-center font-bold">
                    {(domain.branding?.name || domain.name).charAt(0).toUpperCase()}
                  </div>
                )}
                <div>
                  <h3 className="text-lg font-semibold">{domain.branding?.name || domain.name}</h3>
                  {domain.branding?.slogan && (
                    <p className="text-sm text-muted-foreground italic">"{domain.branding.slogan}"</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Theme Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Màu sắc giao diện
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(domain.theme || {}).map(([key, value]) => (
                  <div key={key} className="text-center">
                    <div 
                      className="w-full h-16 rounded-lg border mb-2"
                      style={{ backgroundColor: value }}
                    />
                    <p className="text-sm font-medium capitalize">{key.replace('_', ' ')}</p>
                    <p className="text-xs text-muted-foreground font-mono">{value}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          {domain.features && domain.features.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Tính năng được kích hoạt
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {domain.features.map((feature) => (
                    <Badge key={feature} variant="outline">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* SEO Preview */}
          {(domain.seo?.title || domain.seo?.description) && (
            <Card>
              <CardHeader>
                <CardTitle>SEO</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {domain.seo.title && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Meta Title</h4>
                    <p>{domain.seo.title}</p>
                  </div>
                )}
                {domain.seo.description && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Meta Description</h4>
                    <p className="text-sm">{domain.seo.description}</p>
                  </div>
                )}
                {domain.seo.keywords && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Keywords</h4>
                    <p className="text-sm">{domain.seo.keywords}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Locale Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Ngôn ngữ</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Ngôn ngữ mặc định</h4>
                  <p>{domain.locale?.default || 'vi'}</p>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">Ngôn ngữ hỗ trợ</h4>
                  <div className="flex gap-1">
                    {(domain.locale?.supported || ['vi']).map((locale) => (
                      <Badge key={locale} variant="outline" className="text-xs">
                        {locale}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Mock Preview */}
          <Card>
            <CardHeader>
              <CardTitle>Xem trước giao diện</CardTitle>
            </CardHeader>
            <CardContent>
              <div 
                className="border rounded-lg p-6 space-y-4"
                style={{
                  backgroundColor: domain.theme?.background || '#ffffff',
                  color: domain.theme?.text || '#1e293b'
                }}
              >
                {/* Mock Header */}
                <div className="flex items-center justify-between pb-4 border-b">
                  <div className="flex items-center gap-3">
                    {domain.branding?.logo_url ? (
                      <img 
                        src={domain.branding.logo_url} 
                        alt={domain.branding.name}
                        className="h-8 w-8 object-contain"
                      />
                    ) : (
                      <div 
                        className="h-8 w-8 rounded text-white text-sm flex items-center justify-center font-bold"
                        style={{ backgroundColor: domain.theme?.primary || '#3b82f6' }}
                      >
                        {(domain.branding?.name || domain.name).charAt(0).toUpperCase()}
                      </div>
                    )}
                    <span className="font-semibold">{domain.branding?.name || domain.name}</span>
                  </div>
                  <div className="flex gap-2">
                    <div 
                      className="px-3 py-1 rounded text-sm text-white"
                      style={{ backgroundColor: domain.theme?.primary || '#3b82f6' }}
                    >
                      Đăng nhập
                    </div>
                  </div>
                </div>

                {/* Mock Content */}
                <div className="space-y-3">
                  <h2 className="text-xl font-bold">Chào mừng đến với {domain.branding?.name || domain.name}</h2>
                  {domain.branding?.slogan && (
                    <p style={{ color: domain.theme?.text_secondary || '#64748b' }}>
                      {domain.branding.slogan}
                    </p>
                  )}
                  <div className="flex gap-2">
                    <div 
                      className="px-4 py-2 rounded text-white"
                      style={{ backgroundColor: domain.theme?.primary || '#3b82f6' }}
                    >
                      Bắt đầu
                    </div>
                    <div 
                      className="px-4 py-2 rounded border"
                      style={{ 
                        borderColor: domain.theme?.secondary || '#64748b',
                        color: domain.theme?.secondary || '#64748b'
                      }}
                    >
                      Tìm hiểu thêm
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button onClick={onClose}>
            Đóng
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
