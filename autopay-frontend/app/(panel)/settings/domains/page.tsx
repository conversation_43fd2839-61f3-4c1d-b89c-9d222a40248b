'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Globe, CheckCircle, AlertCircle, Settings } from 'lucide-react';
import { DomainForm } from './components/domain-form';
import { DomainVerification } from './components/domain-verification';

interface Domain {
  id: string;
  hostname: string;
  name: string;
  description?: string;
  domain_type: 'subdomain' | 'custom';
  status: 'pending' | 'active' | 'failed' | 'suspended';
  is_verified: boolean;
  is_primary: boolean;
  is_active: boolean;
  verified_at?: string;
  created_at: string;
}

export default function DomainsPage() {
  const [domains, setDomains] = useState<Domain[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [showVerification, setShowVerification] = useState(false);
  const [selectedDomain, setSelectedDomain] = useState<Domain | null>(null);

  useEffect(() => {
    fetchDomains();
  }, []);

  const fetchDomains = async () => {
    try {
      setIsLoading(true);
      // Get current organization ID from context/auth
      const orgId = 'current-org-id'; // Replace with actual org ID
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/organizations/${orgId}/domains`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Accept': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setDomains(data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching domains:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddDomain = () => {
    setSelectedDomain(null);
    setShowForm(true);
  };

  const handleEditDomain = (domain: Domain) => {
    setSelectedDomain(domain);
    setShowForm(true);
  };

  const handleVerifyDomain = (domain: Domain) => {
    setSelectedDomain(domain);
    setShowVerification(true);
  };

  const handleSetPrimary = async (domainId: string) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/domains/${domainId}/set-primary`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Accept': 'application/json',
        },
      });

      if (response.ok) {
        fetchDomains(); // Refresh list
      }
    } catch (error) {
      console.error('Error setting primary domain:', error);
    }
  };

  const getStatusBadge = (domain: Domain) => {
    if (!domain.is_verified) {
      return <Badge variant="outline" className="text-yellow-600">Chưa xác thực</Badge>;
    }
    
    switch (domain.status) {
      case 'active':
        return <Badge variant="default" className="bg-green-500">Hoạt động</Badge>;
      case 'pending':
        return <Badge variant="outline" className="text-blue-600">Đang chờ</Badge>;
      case 'failed':
        return <Badge variant="destructive">Lỗi</Badge>;
      case 'suspended':
        return <Badge variant="outline" className="text-red-600">Tạm dừng</Badge>;
      default:
        return <Badge variant="outline">Không xác định</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Quản lý Domain</h1>
          <p className="text-muted-foreground">
            Quản lý các domain cho tổ chức của bạn
          </p>
        </div>
        <Button onClick={handleAddDomain}>
          <Plus className="h-4 w-4 mr-2" />
          Thêm Domain
        </Button>
      </div>

      <div className="grid gap-4">
        {domains.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Globe className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">Chưa có domain nào</h3>
              <p className="text-muted-foreground text-center mb-4">
                Thêm domain đầu tiên để bắt đầu sử dụng white-label
              </p>
              <Button onClick={handleAddDomain}>
                <Plus className="h-4 w-4 mr-2" />
                Thêm Domain
              </Button>
            </CardContent>
          </Card>
        ) : (
          domains.map((domain) => (
            <Card key={domain.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Globe className="h-5 w-5" />
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {domain.hostname}
                        {domain.is_primary && (
                          <Badge variant="outline" className="text-blue-600">
                            Chính
                          </Badge>
                        )}
                      </CardTitle>
                      <CardDescription>{domain.name}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(domain)}
                    <Badge variant="outline">
                      {domain.domain_type === 'custom' ? 'Custom' : 'Subdomain'}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    {domain.is_verified ? (
                      <div className="flex items-center gap-1">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        Đã xác thực
                      </div>
                    ) : (
                      <div className="flex items-center gap-1">
                        <AlertCircle className="h-4 w-4 text-yellow-500" />
                        Chưa xác thực
                      </div>
                    )}
                    <span>Tạo: {new Date(domain.created_at).toLocaleDateString('vi-VN')}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {!domain.is_verified && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleVerifyDomain(domain)}
                      >
                        Xác thực
                      </Button>
                    )}
                    
                    {domain.is_verified && !domain.is_primary && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetPrimary(domain.id)}
                      >
                        Đặt làm chính
                      </Button>
                    )}
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditDomain(domain)}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {showForm && (
        <DomainForm
          domain={selectedDomain}
          onClose={() => {
            setShowForm(false);
            setSelectedDomain(null);
            fetchDomains();
          }}
        />
      )}

      {showVerification && selectedDomain && (
        <DomainVerification
          domain={selectedDomain}
          onClose={() => {
            setShowVerification(false);
            setSelectedDomain(null);
            fetchDomains();
          }}
        />
      )}
    </div>
  );
}
