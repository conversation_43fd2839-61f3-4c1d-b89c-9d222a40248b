'use client';

import { <PERSON>lider } from '@/components/ui/slider';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import { X } from 'lucide-react';
import React from 'react';
import { GoQuestion } from 'react-icons/go';

const organizationFormSchema = z.object({
  // Domain configuration fields
  domainConfig: z.object({
    brand_name: z.string().optional(),
    slogan: z.string().optional(),
    logo_url: z.string().url().optional().or(z.literal('')),
    favicon_url: z.string().url().optional().or(z.literal('')),
    theme_colors: z.object({
      primary: z.string(),
      secondary: z.string(),
      accent: z.string(),
      background: z.string(),
      surface: z.string(),
      text: z.string(),
      text_secondary: z.string(),
    }),
    meta_title: z.string().optional(),
    meta_description: z.string().optional(),
    meta_keywords: z.string().optional(),
    og_image_url: z.string().url().optional().or(z.literal('')),
    enabled_features: z.array(z.string()).optional(),
  }),
});

type OrganizationFormValues = z.infer<typeof organizationFormSchema>;

// This can come from your database or API.
const defaultValues: Partial<OrganizationFormValues> = {
  domainConfig: {
    brand_name: '',
    slogan: '',
    logo_url: '',
    favicon_url: '',
    theme_colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
      text_secondary: '#64748b',
    },
    meta_title: '',
    meta_description: '',
    meta_keywords: '',
    og_image_url: '',
    enabled_features: [],
  },
};

export function ProfileForm() {
  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationFormSchema),
    defaultValues,
    mode: 'onChange',
  });

  const [newFeature, setNewFeature] = React.useState('');

  function onSubmit(data: OrganizationFormValues) {
    toast({
      title: 'Cập nhật cấu hình tổ chức thành công:',
      description: (
        <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
          <code className="text-white">{JSON.stringify(data, null, 2)}</code>
        </pre>
      ),
    });
  }

  const handleAddFeature = () => {
    const currentFeatures =
      form.getValues('domainConfig.enabled_features') || [];
    if (newFeature.trim() && !currentFeatures.includes(newFeature.trim())) {
      form.setValue('domainConfig.enabled_features', [
        ...currentFeatures,
        newFeature.trim(),
      ]);
      setNewFeature('');
    }
  };

  const handleRemoveFeature = (feature: string) => {
    const currentFeatures =
      form.getValues('domainConfig.enabled_features') || [];
    form.setValue(
      'domainConfig.enabled_features',
      currentFeatures.filter((f) => f !== feature),
    );
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">Cấu hình tổ chức</h2>
            <p className="text-sm text-muted-foreground">
              Quản lý thông tin thương hiệu và giao diện cho tổ chức của bạn
            </p>
          </div>

          <Tabs defaultValue="branding" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="branding">Thương hiệu</TabsTrigger>
              <TabsTrigger value="theme">Giao diện</TabsTrigger>
              <TabsTrigger value="advanced">Nâng cao</TabsTrigger>
            </TabsList>
            <TabsContent value="branding" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="domainConfig.brand_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tên thương hiệu</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="AutoPAY" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="domainConfig.slogan"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Slogan</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Thanh toán tự động thông minh"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="domainConfig.logo_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>URL Logo</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="https://example.com/logo.png"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="domainConfig.favicon_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>URL Favicon</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="https://example.com/favicon.ico"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              <TabsContent value="theme" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {[
                    'primary',
                    'secondary',
                    'accent',
                    'background',
                    'surface',
                    'text',
                    'text_secondary',
                  ].map((colorKey) => (
                    <FormField
                      key={colorKey}
                      control={form.control}
                      name={`domainConfig.theme_colors.${colorKey}` as any}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="capitalize">
                            {colorKey.replace('_', ' ')}
                          </FormLabel>
                          <div className="flex gap-2">
                            <FormControl>
                              <Input
                                type="color"
                                {...field}
                                className="h-10 w-16"
                              />
                            </FormControl>
                            <FormControl>
                              <Input
                                {...field}
                                placeholder="#000000"
                                className="flex-1"
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                {/* SEO Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">SEO</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="domainConfig.meta_title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Meta Title</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="AutoPAY - Thanh toán tự động"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="domainConfig.meta_description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Meta Description</FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              placeholder="Mô tả cho SEO..."
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="domainConfig.meta_keywords"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Meta Keywords</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="thanh toán, tự động, autopay"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>

                {/* Features */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Tính năng</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex gap-2">
                      <Input
                        value={newFeature}
                        onChange={(e) => setNewFeature(e.target.value)}
                        placeholder="Tên tính năng..."
                        onKeyDown={(e) =>
                          e.key === 'Enter' &&
                          (e.preventDefault(), handleAddFeature())
                        }
                      />
                      <Button type="button" onClick={handleAddFeature}>
                        Thêm
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {(form.watch('domainConfig.enabled_features') || []).map(
                        (feature) => (
                          <Badge
                            key={feature}
                            variant="secondary"
                            className="cursor-pointer"
                          >
                            {feature}
                            <X
                              className="ml-1 h-3 w-3"
                              onClick={() => handleRemoveFeature(feature)}
                            />
                          </Badge>
                        ),
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <Button type="submit" size="sm">
            Cập nhật cấu hình
          </Button>
        </div>
      </form>
    </Form>
  );
}
