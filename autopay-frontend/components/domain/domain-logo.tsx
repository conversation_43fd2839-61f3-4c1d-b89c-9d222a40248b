'use client'

import { useDomain } from '@/lib/hooks/use-domain'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import React from 'react'

interface DomainLogoProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
}

export function DomainLogo({ className, size = 'md', showText = true }: DomainLogoProps): JSX.Element {
  const { config } = useDomain()

  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-xl',
  }

  const logoUrl = config?.branding?.logo_url
  const brandName = config?.branding?.name || config?.name || 'AutoPAY'

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {logoUrl ? (
        <Image
          src={logoUrl}
          alt={brandName}
          width={size === 'sm' ? 24 : size === 'md' ? 32 : 48}
          height={size === 'sm' ? 24 : size === 'md' ? 32 : 48}
          className={cn('object-contain', sizeClasses[size])}
        />
      ) : (
        <div className={cn(
          'flex items-center justify-center rounded bg-primary text-primary-foreground font-bold',
          sizeClasses[size],
          textSizeClasses[size]
        )}>
          {brandName.charAt(0).toUpperCase()}
        </div>
      )}
      
      {showText && (
        <span className={cn('font-semibold', textSizeClasses[size])}>
          {brandName}
        </span>
      )}
    </div>
  )
}
