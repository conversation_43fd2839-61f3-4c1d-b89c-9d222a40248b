<?php

namespace Modules\Core\Repositories;

use Modules\Core\Models\Domain;
use Illuminate\Database\Eloquent\Collection;

class DomainRepository
{
    public function __construct(
        protected Domain $model
    ) {}

    /**
     * Get all active domains.
     */
    public function getAllActive(): Collection
    {
        return $this->model->active()->get();
    }

    /**
     * Find domain by hostname.
     */
    public function findByHostname(string $hostname): ?Domain
    {
        return $this->model->byHostname($hostname)->active()->first();
    }

    /**
     * Get default domain.
     */
    public function getDefault(): ?Domain
    {
        return $this->model->default()->active()->first();
    }

    /**
     * Create a new domain.
     */
    public function create(array $data): Domain
    {
        return $this->model->create($data);
    }

    /**
     * Update domain by ID.
     */
    public function update(string $id, array $data): bool
    {
        return $this->model->where('id', $id)->update($data);
    }

    /**
     * Delete domain by ID.
     */
    public function delete(string $id): bool
    {
        return $this->model->where('id', $id)->delete();
    }

    /**
     * Find domain by ID.
     */
    public function findById(string $id): ?Domain
    {
        return $this->model->find($id);
    }

    /**
     * Get domains by organization.
     */
    public function getByOrganization(string $organizationId): Collection
    {
        return $this->model->where('organization_id', $organizationId)->get();
    }

    /**
     * Set domain as default.
     */
    public function setAsDefault(string $id): bool
    {
        // First, unset all other domains as default
        $this->model->where('is_default', true)->update(['is_default' => false]);
        
        // Then set the specified domain as default
        return $this->model->where('id', $id)->update(['is_default' => true]);
    }

    /**
     * Check if hostname is available.
     */
    public function isHostnameAvailable(string $hostname, ?string $excludeId = null): bool
    {
        $query = $this->model->where('hostname', $hostname);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return !$query->exists();
    }
}
