<?php

namespace Modules\Core\Services;

use Modules\Core\Models\Domain;
use Modules\Core\Repositories\DomainRepository;
use Modules\Core\Services\DomainVerificationService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class DomainService
{
    public function __construct(
        protected DomainRepository $domainRepository,
        protected DomainVerificationService $verificationService
    ) {}

    /**
     * Get domain configuration by hostname.
     */
    public function getDomainConfig(string $hostname): ?array
    {
        $cacheKey = "domain_config_{$hostname}";

        return Cache::remember($cacheKey, 3600, function () use ($hostname) {
            $domain = $this->domainRepository->findByHostname($hostname);

            if (!$domain) {
                // Fallback to default domain
                $domain = $this->domainRepository->getDefault();
            }

            return $domain?->config;
        });
    }

    /**
     * Get all active domains.
     */
    public function getAllDomains(): Collection
    {
        return $this->domainRepository->getAllActive();
    }

    /**
     * Create a new domain.
     */
    public function createDomain(array $data): Domain
    {
        $this->validateDomainData($data);

        // Check hostname availability
        if (!$this->domainRepository->isHostnameAvailable($data['hostname'])) {
            throw ValidationException::withMessages([
                'hostname' => ['The hostname is already taken.']
            ]);
        }

        $domain = $this->domainRepository->create($data);

        // Clear cache
        $this->clearDomainCache($data['hostname']);

        return $domain;
    }

    /**
     * Update domain.
     */
    public function updateDomain(string $id, array $data): bool
    {
        $domain = $this->domainRepository->findById($id);

        if (!$domain) {
            throw new \Exception('Domain not found.');
        }

        $this->validateDomainData($data, $id);

        // Check hostname availability if hostname is being changed
        if (isset($data['hostname']) && $data['hostname'] !== $domain->hostname) {
            if (!$this->domainRepository->isHostnameAvailable($data['hostname'], $id)) {
                throw ValidationException::withMessages([
                    'hostname' => ['The hostname is already taken.']
                ]);
            }
        }

        $result = $this->domainRepository->update($id, $data);

        // Clear cache for both old and new hostnames
        $this->clearDomainCache($domain->hostname);
        if (isset($data['hostname'])) {
            $this->clearDomainCache($data['hostname']);
        }

        return $result;
    }

    /**
     * Delete domain.
     */
    public function deleteDomain(string $id): bool
    {
        $domain = $this->domainRepository->findById($id);

        if (!$domain) {
            throw new \Exception('Domain not found.');
        }

        if ($domain->is_default) {
            throw new \Exception('Cannot delete the default domain.');
        }

        $result = $this->domainRepository->delete($id);

        // Clear cache
        $this->clearDomainCache($domain->hostname);

        return $result;
    }

    /**
     * Set domain as default.
     */
    public function setAsDefault(string $id): bool
    {
        $domain = $this->domainRepository->findById($id);

        if (!$domain) {
            throw new \Exception('Domain not found.');
        }

        $result = $this->domainRepository->setAsDefault($id);

        // Clear all domain caches since default status changed
        $this->clearAllDomainCaches();

        return $result;
    }

    /**
     * Get domains by organization.
     */
    public function getDomainsByOrganization(string $organizationId): Collection
    {
        return $this->domainRepository->getByOrganization($organizationId);
    }

    /**
     * Get domain by ID.
     */
    public function getDomainById(string $id): ?Domain
    {
        return $this->domainRepository->findById($id);
    }

    /**
     * Validate domain data.
     */
    protected function validateDomainData(array $data, ?string $excludeId = null): void
    {
        $rules = [
            'hostname' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'brand_name' => 'nullable|string|max:255',
            'slogan' => 'nullable|string|max:500',
            'logo_url' => 'nullable|url',
            'favicon_url' => 'nullable|url',
            'theme_colors' => 'nullable|array',
            'custom_css' => 'nullable|array',
            'font_family' => 'nullable|string|max:255',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'og_image_url' => 'nullable|url',
            'enabled_features' => 'nullable|array',
            'feature_configs' => 'nullable|array',
            'default_locale' => 'nullable|string|max:10',
            'supported_locales' => 'nullable|array',
            'contact_info' => 'nullable|array',
            'custom_config' => 'nullable|array',
            'is_active' => 'nullable|boolean',
            'is_default' => 'nullable|boolean',
            'force_https' => 'nullable|boolean',
            'redirect_to' => 'nullable|url',
            'organization_id' => 'nullable|exists:organizations,id',
        ];

        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Clear domain cache.
     */
    protected function clearDomainCache(string $hostname): void
    {
        Cache::forget("domain_config_{$hostname}");
    }

    /**
     * Clear all domain caches.
     */
    protected function clearAllDomainCaches(): void
    {
        $domains = $this->domainRepository->getAllActive();

        foreach ($domains as $domain) {
            $this->clearDomainCache($domain->hostname);
        }
    }

    /**
     * Get domains by organization.
     */
    public function getDomainsByOrganization(string $organizationId): Collection
    {
        return $this->domainRepository->getByOrganization($organizationId);
    }

    /**
     * Setup domain for organization.
     */
    public function setupDomainForOrganization(
        string $hostname,
        string $organizationId,
        array $config = []
    ): Domain {
        return $this->verificationService->setupDomainForOrganization(
            $hostname,
            $organizationId,
            $config
        );
    }

    /**
     * Create domain verification.
     */
    public function createDomainVerification(Domain $domain, string $type = 'dns'): array
    {
        $verification = $this->verificationService->createVerification($domain, $type);

        return [
            'verification_id' => $verification->id,
            'type' => $verification->verification_type,
            'token' => $verification->verification_token,
            'instructions' => $verification->instructions,
            'expires_at' => $verification->expires_at,
        ];
    }

    /**
     * Verify domain.
     */
    public function verifyDomain(string $verificationId): bool
    {
        $verification = \Modules\Core\Models\DomainVerification::find($verificationId);

        if (!$verification) {
            throw new \Exception('Verification not found');
        }

        return $this->verificationService->verifyDomain($verification);
    }

    /**
     * Get verification instructions.
     */
    public function getVerificationInstructions(Domain $domain, string $type = 'dns'): array
    {
        return $this->verificationService->getVerificationInstructions($domain, $type);
    }

    /**
     * Set domain as primary for organization.
     */
    public function setPrimaryDomain(string $domainId): bool
    {
        $domain = $this->getDomainById($domainId);

        if (!$domain || !$domain->isVerified()) {
            throw new \Exception('Domain must be verified before setting as primary');
        }

        $domain->setAsPrimary();
        $this->clearDomainCache($domain->hostname);

        return true;
    }

    /**
     * Get organization's primary domain.
     */
    public function getOrganizationPrimaryDomain(string $organizationId): ?Domain
    {
        return Domain::byOrganization($organizationId)
                    ->primary()
                    ->verified()
                    ->first();
    }

    /**
     * Validate domain format.
     */
    public function validateDomainFormat(string $hostname): array
    {
        return $this->verificationService->validateDomainFormat($hostname);
    }
}
