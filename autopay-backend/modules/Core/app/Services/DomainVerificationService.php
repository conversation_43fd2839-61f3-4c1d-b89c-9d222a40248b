<?php

namespace Modules\Core\Services;

use Modules\Core\Models\Domain;
use Modules\Core\Models\DomainVerification;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class DomainVerificationService
{
    /**
     * Create verification for domain.
     */
    public function createVerification(Domain $domain, string $type = 'dns'): DomainVerification
    {
        return DomainVerification::createForDomain($domain, $type);
    }

    /**
     * Verify domain using specified method.
     */
    public function verifyDomain(DomainVerification $verification): bool
    {
        try {
            $result = match ($verification->verification_type) {
                'dns' => $this->verifyDnsRecord($verification),
                'file' => $this->verifyFileUpload($verification),
                'meta' => $this->verifyMetaTag($verification),
                default => false,
            };

            if ($result) {
                $verification->markAsVerified();
                Log::info("Domain verification successful", [
                    'domain_id' => $verification->domain_id,
                    'type' => $verification->verification_type,
                ]);
                return true;
            } else {
                $verification->recordAttempt('Verification failed');
                return false;
            }
        } catch (Exception $e) {
            $verification->recordAttempt($e->getMessage());
            Log::error("Domain verification error", [
                'domain_id' => $verification->domain_id,
                'type' => $verification->verification_type,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Verify DNS TXT record.
     */
    protected function verifyDnsRecord(DomainVerification $verification): bool
    {
        $domain = $verification->domain;
        $recordName = "_autopay-verification.{$domain->hostname}";
        
        try {
            // Use DNS lookup to check TXT record
            $records = dns_get_record($recordName, DNS_TXT);
            
            foreach ($records as $record) {
                if (isset($record['txt']) && $record['txt'] === $verification->verification_token) {
                    return true;
                }
            }
            
            return false;
        } catch (Exception $e) {
            Log::error("DNS verification failed", [
                'domain' => $domain->hostname,
                'record_name' => $recordName,
                'token' => $verification->verification_token,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Verify file upload method.
     */
    protected function verifyFileUpload(DomainVerification $verification): bool
    {
        $domain = $verification->domain;
        $url = "https://{$domain->hostname}/.well-known/autopay-verification.txt";
        
        try {
            $response = Http::timeout(10)->get($url);
            
            if ($response->successful()) {
                $content = trim($response->body());
                return $content === $verification->verification_token;
            }
            
            return false;
        } catch (Exception $e) {
            Log::error("File verification failed", [
                'domain' => $domain->hostname,
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Verify HTML meta tag method.
     */
    protected function verifyMetaTag(DomainVerification $verification): bool
    {
        $domain = $verification->domain;
        $url = "https://{$domain->hostname}";
        
        try {
            $response = Http::timeout(10)->get($url);
            
            if ($response->successful()) {
                $html = $response->body();
                
                // Look for meta tag with verification token
                $pattern = '/<meta\s+name=["\']autopay-verification["\']\s+content=["\']' . 
                          preg_quote($verification->verification_token, '/') . 
                          '["\']\s*\/?>/i';
                
                return preg_match($pattern, $html) === 1;
            }
            
            return false;
        } catch (Exception $e) {
            Log::error("Meta tag verification failed", [
                'domain' => $domain->hostname,
                'url' => $url,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Check all pending verifications.
     */
    public function checkPendingVerifications(): array
    {
        $results = [];
        $pendingVerifications = DomainVerification::pending()->get();
        
        foreach ($pendingVerifications as $verification) {
            $result = $this->verifyDomain($verification);
            $results[] = [
                'verification_id' => $verification->id,
                'domain_hostname' => $verification->domain->hostname,
                'type' => $verification->verification_type,
                'verified' => $result,
            ];
        }
        
        return $results;
    }

    /**
     * Get verification instructions for domain.
     */
    public function getVerificationInstructions(Domain $domain, string $type = 'dns'): array
    {
        $verification = $domain->activeVerifications()
                              ->where('verification_type', $type)
                              ->first();
        
        if (!$verification) {
            $verification = $this->createVerification($domain, $type);
        }
        
        return $verification->instructions;
    }

    /**
     * Validate domain format.
     */
    public function validateDomainFormat(string $hostname): array
    {
        $errors = [];
        
        // Basic format validation
        if (!filter_var($hostname, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME)) {
            $errors[] = 'Invalid domain format';
        }
        
        // Check for reserved domains
        $reservedDomains = ['localhost', 'autopay.vn', 'www.autopay.vn'];
        if (in_array(strtolower($hostname), $reservedDomains)) {
            $errors[] = 'Domain is reserved and cannot be used';
        }
        
        // Check if domain already exists
        if (Domain::where('hostname', $hostname)->exists()) {
            $errors[] = 'Domain is already registered';
        }
        
        return $errors;
    }

    /**
     * Setup domain for organization.
     */
    public function setupDomainForOrganization(
        string $hostname, 
        string $organizationId, 
        array $config = []
    ): Domain {
        // Validate domain
        $errors = $this->validateDomainFormat($hostname);
        if (!empty($errors)) {
            throw new Exception('Domain validation failed: ' . implode(', ', $errors));
        }
        
        // Determine domain type
        $domainType = $this->isDomainCustom($hostname) ? 'custom' : 'subdomain';
        
        // Create domain
        $domain = Domain::create(array_merge([
            'organization_id' => $organizationId,
            'hostname' => $hostname,
            'name' => $config['name'] ?? $hostname,
            'domain_type' => $domainType,
            'status' => 'pending',
            'is_active' => true,
            'is_verified' => false,
        ], $config));
        
        // Create initial verification
        $this->createVerification($domain, 'dns');
        
        return $domain;
    }

    /**
     * Check if domain is custom (not subdomain).
     */
    protected function isDomainCustom(string $hostname): bool
    {
        $appDomain = config('app.domain', 'autopay.vn');
        return !str_ends_with($hostname, '.' . $appDomain);
    }
}
