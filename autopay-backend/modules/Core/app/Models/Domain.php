<?php

namespace Modules\Core\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Organization\Models\Organization;

class Domain extends Model
{
    use HasFactory, HasUlids;

    protected $fillable = [
        'organization_id',
        'hostname',
        'name',
        'description',
        'brand_name',
        'slogan',
        'logo_url',
        'favicon_url',
        'theme_colors',
        'custom_css',
        'font_family',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'og_image_url',
        'enabled_features',
        'feature_configs',
        'default_locale',
        'supported_locales',
        'contact_info',
        'custom_config',
        'is_active',
        'is_default',
        'force_https',
        'redirect_to',
        // New verification fields
        'is_verified',
        'verified_at',
        'ssl_enabled',
        'ssl_expires_at',
        'domain_type',
        'status',
        'is_primary',
        'dns_records',
    ];

    protected $casts = [
        'theme_colors' => 'array',
        'custom_css' => 'array',
        'enabled_features' => 'array',
        'feature_configs' => 'array',
        'supported_locales' => 'array',
        'contact_info' => 'array',
        'custom_config' => 'array',
        'dns_records' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'force_https' => 'boolean',
        'is_verified' => 'boolean',
        'ssl_enabled' => 'boolean',
        'is_primary' => 'boolean',
        'verified_at' => 'datetime',
        'ssl_expires_at' => 'datetime',
    ];

    /**
     * Get the organization that owns the domain.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get domain verifications.
     */
    public function verifications(): HasMany
    {
        return $this->hasMany(DomainVerification::class);
    }

    /**
     * Get active domain verifications.
     */
    public function activeVerifications(): HasMany
    {
        return $this->verifications()->active();
    }

    /**
     * Scope to get active domains.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get default domain.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Get domain by hostname.
     */
    public function scopeByHostname($query, string $hostname)
    {
        return $query->where('hostname', $hostname);
    }

    /**
     * Scope to get verified domains.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get primary domains.
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Scope to get domains by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('domain_type', $type);
    }

    /**
     * Scope to get domains by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get domains by organization.
     */
    public function scopeByOrganization($query, string $organizationId)
    {
        return $query->where('organization_id', $organizationId);
    }

    /**
     * Get the full theme configuration with defaults.
     */
    public function getThemeConfigAttribute(): array
    {
        $defaultTheme = [
            'primary' => '#3b82f6',
            'secondary' => '#64748b',
            'accent' => '#f59e0b',
            'background' => '#ffffff',
            'surface' => '#f8fafc',
            'text' => '#1e293b',
            'text_secondary' => '#64748b',
        ];

        return array_merge($defaultTheme, $this->theme_colors ?? []);
    }

    /**
     * Get the full branding configuration.
     */
    public function getBrandingConfigAttribute(): array
    {
        return [
            'name' => $this->brand_name ?? $this->name,
            'slogan' => $this->slogan,
            'logo_url' => $this->logo_url,
            'favicon_url' => $this->favicon_url,
        ];
    }

    /**
     * Get the SEO configuration.
     */
    public function getSeoConfigAttribute(): array
    {
        return [
            'title' => $this->meta_title ?? $this->name,
            'description' => $this->meta_description,
            'keywords' => $this->meta_keywords,
            'og_image' => $this->og_image_url,
        ];
    }

    /**
     * Check if a feature is enabled for this domain.
     */
    public function hasFeature(string $feature): bool
    {
        return in_array($feature, $this->enabled_features ?? []);
    }

    /**
     * Get configuration for a specific feature.
     */
    public function getFeatureConfig(string $feature): array
    {
        return $this->feature_configs[$feature] ?? [];
    }

    /**
     * Get the full domain configuration for frontend.
     */
    public function getConfigAttribute(): array
    {
        return [
            'id' => $this->id,
            'hostname' => $this->hostname,
            'name' => $this->name,
            'description' => $this->description,
            'branding' => $this->branding_config,
            'theme' => $this->theme_config,
            'seo' => $this->seo_config,
            'features' => $this->enabled_features ?? [],
            'feature_configs' => $this->feature_configs ?? [],
            'locale' => [
                'default' => $this->default_locale,
                'supported' => $this->supported_locales ?? [$this->default_locale],
            ],
            'contact' => $this->contact_info ?? [],
            'custom' => $this->custom_config ?? [],
            'font_family' => $this->font_family,
            'custom_css' => $this->custom_css ?? [],
        ];
    }

    /**
     * Check if domain is verified.
     */
    public function isVerified(): bool
    {
        return $this->is_verified;
    }

    /**
     * Check if domain is primary for organization.
     */
    public function isPrimary(): bool
    {
        return $this->is_primary;
    }

    /**
     * Check if domain is custom (not subdomain).
     */
    public function isCustomDomain(): bool
    {
        return $this->domain_type === 'custom';
    }

    /**
     * Check if domain is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && $this->is_active;
    }

    /**
     * Set as primary domain for organization.
     */
    public function setAsPrimary(): void
    {
        // Unset other primary domains for this organization
        static::where('organization_id', $this->organization_id)
              ->where('id', '!=', $this->id)
              ->update(['is_primary' => false]);

        // Set this domain as primary
        $this->update(['is_primary' => true]);
    }

    /**
     * Generate required DNS records for verification.
     */
    public function generateDnsRecords(): array
    {
        $records = [];

        // Verification record
        if (!$this->is_verified) {
            $verification = $this->activeVerifications()
                                ->where('verification_type', 'dns')
                                ->first();

            if ($verification) {
                $records[] = [
                    'type' => 'TXT',
                    'name' => "_autopay-verification",
                    'value' => $verification->verification_token,
                    'purpose' => 'Domain verification',
                ];
            }
        }

        // CNAME record for custom domains
        if ($this->domain_type === 'custom') {
            $records[] = [
                'type' => 'CNAME',
                'name' => '@',
                'value' => config('app.domain', 'autopay.vn'),
                'purpose' => 'Domain pointing',
            ];
        }

        return $records;
    }

    /**
     * Create verification for this domain.
     */
    public function createVerification(string $type = 'dns'): DomainVerification
    {
        return DomainVerification::createForDomain($this, $type);
    }
}
