<?php

namespace Modules\Core\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class DomainVerification extends Model
{
    use HasFactory, HasUlids;

    protected $fillable = [
        'domain_id',
        'verification_type',
        'verification_token',
        'verification_value',
        'verification_instructions',
        'is_verified',
        'verified_at',
        'expires_at',
        'verification_attempts',
        'last_check_at',
        'last_error',
        'verification_data',
    ];

    protected $casts = [
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'expires_at' => 'datetime',
        'last_check_at' => 'datetime',
        'verification_data' => 'array',
        'verification_attempts' => 'integer',
    ];

    /**
     * Verification types
     */
    const TYPE_DNS = 'dns';
    const TYPE_FILE = 'file';
    const TYPE_META = 'meta';

    /**
     * Get the domain that owns this verification.
     */
    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }

    /**
     * Scope to get active verifications (not expired).
     */
    public function scopeActive($query)
    {
        return $query->where('expires_at', '>', now())
                    ->orWhereNull('expires_at');
    }

    /**
     * Scope to get verified records.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get pending verifications.
     */
    public function scopePending($query)
    {
        return $query->where('is_verified', false)
                    ->where('expires_at', '>', now());
    }

    /**
     * Check if verification is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Mark verification as verified.
     */
    public function markAsVerified(): void
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => now(),
            'last_error' => null,
        ]);

        // Update domain verification status
        $this->domain->update([
            'is_verified' => true,
            'verified_at' => now(),
            'status' => 'active',
        ]);
    }

    /**
     * Record verification attempt.
     */
    public function recordAttempt(?string $error = null): void
    {
        $this->increment('verification_attempts');
        $this->update([
            'last_check_at' => now(),
            'last_error' => $error,
        ]);
    }

    /**
     * Generate verification instructions based on type.
     */
    public function getInstructionsAttribute(): array
    {
        $domain = $this->domain;
        
        switch ($this->verification_type) {
            case self::TYPE_DNS:
                return [
                    'type' => 'DNS Record',
                    'record_type' => 'TXT',
                    'name' => "_autopay-verification.{$domain->hostname}",
                    'value' => $this->verification_token,
                    'description' => 'Add this TXT record to your DNS settings',
                ];

            case self::TYPE_FILE:
                return [
                    'type' => 'File Upload',
                    'file_path' => "/.well-known/autopay-verification.txt",
                    'file_content' => $this->verification_token,
                    'url' => "https://{$domain->hostname}/.well-known/autopay-verification.txt",
                    'description' => 'Upload this file to your website root',
                ];

            case self::TYPE_META:
                return [
                    'type' => 'HTML Meta Tag',
                    'tag' => "<meta name=\"autopay-verification\" content=\"{$this->verification_token}\" />",
                    'description' => 'Add this meta tag to your website\'s <head> section',
                ];

            default:
                return [];
        }
    }

    /**
     * Generate a new verification token.
     */
    public static function generateToken(): string
    {
        return 'autopay_' . bin2hex(random_bytes(16));
    }

    /**
     * Create verification for domain.
     */
    public static function createForDomain(Domain $domain, string $type): self
    {
        // Deactivate existing verifications for this domain and type
        static::where('domain_id', $domain->id)
              ->where('verification_type', $type)
              ->update(['expires_at' => now()]);

        return static::create([
            'domain_id' => $domain->id,
            'verification_type' => $type,
            'verification_token' => static::generateToken(),
            'expires_at' => now()->addDays(7), // Token expires in 7 days
            'verification_instructions' => null, // Will be generated dynamically
        ]);
    }
}
