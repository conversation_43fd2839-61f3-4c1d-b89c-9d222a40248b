<?php

namespace Modules\Core\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Core\Services\DomainService;
use Illuminate\Validation\ValidationException;
use <PERSON>ymfony\Component\HttpFoundation\Response;

class DomainController extends Controller
{
    public function __construct(
        protected DomainService $domainService
    ) {}

    /**
     * Get domain configuration by hostname.
     */
    public function getConfig(Request $request): Response
    {
        $hostname = $request->get('hostname', $request->getHost());

        $config = $this->domainService->getDomainConfig($hostname);

        if (!$config) {
            return ResponseHelper::error('Domain configuration not found', null, 404);
        }

        return ResponseHelper::success(data: $config);
    }

    /**
     * Get all domains.
     */
    public function index(): Response
    {
        $domains = $this->domainService->getAllDomains();

        return ResponseHelper::success(data: $domains);
    }

    /**
     * Create a new domain.
     */
    public function store(Request $request): Response
    {
        try {
            $domain = $this->domainService->createDomain($request->all());

            return ResponseHelper::success('Domain created successfully', $domain, 201);
        } catch (ValidationException $e) {
            return ResponseHelper::error('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Get a specific domain.
     */
    public function show(string $id): Response
    {
        $domain = $this->domainService->getDomainById($id);

        if (!$domain) {
            return ResponseHelper::error('Domain not found', null, 404);
        }

        return ResponseHelper::success(data: $domain);
    }

    /**
     * Update a domain.
     */
    public function update(Request $request, string $id): Response
    {
        try {
            $result = $this->domainService->updateDomain($id, $request->all());

            if (!$result) {
                return ResponseHelper::error('Failed to update domain', null, 400);
            }

            return ResponseHelper::success('Domain updated successfully');
        } catch (ValidationException $e) {
            return ResponseHelper::error('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Delete a domain.
     */
    public function destroy(string $id): Response
    {
        try {
            $result = $this->domainService->deleteDomain($id);

            if (!$result) {
                return ResponseHelper::error('Failed to delete domain', null, 400);
            }

            return ResponseHelper::success('Domain deleted successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Set domain as default.
     */
    public function setDefault(string $id): Response
    {
        try {
            $result = $this->domainService->setAsDefault($id);

            if (!$result) {
                return ResponseHelper::error('Failed to set domain as default', null, 400);
            }

            return ResponseHelper::success('Domain set as default successfully');
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Get domains by organization.
     */
    public function getByOrganization(string $organizationId): Response
    {
        $domains = $this->domainService->getDomainsByOrganization($organizationId);

        return ResponseHelper::success(data: $domains);
    }

    /**
     * Setup domain for organization.
     */
    public function setupDomain(Request $request): Response
    {
        $request->validate([
            'hostname' => 'required|string|max:255',
            'organization_id' => 'required|string|exists:organizations,id',
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
        ]);

        try {
            // Validate domain format
            $errors = $this->domainService->validateDomainFormat($request->hostname);
            if (!empty($errors)) {
                return ResponseHelper::error('Domain validation failed', $errors, 422);
            }

            $domain = $this->domainService->setupDomainForOrganization(
                $request->hostname,
                $request->organization_id,
                $request->only(['name', 'description'])
            );

            return ResponseHelper::success('Domain setup initiated', $domain, 201);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Create domain verification.
     */
    public function createVerification(Request $request, string $id): Response
    {
        $request->validate([
            'type' => 'required|in:dns,file,meta',
        ]);

        try {
            $domain = $this->domainService->getDomainById($id);
            if (!$domain) {
                return ResponseHelper::error('Domain not found', null, 404);
            }

            $verification = $this->domainService->createDomainVerification(
                $domain,
                $request->type
            );

            return ResponseHelper::success('Verification created', $verification);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Verify domain.
     */
    public function verifyDomain(Request $request): Response
    {
        $request->validate([
            'verification_id' => 'required|string|exists:domain_verifications,id',
        ]);

        try {
            $result = $this->domainService->verifyDomain($request->verification_id);

            if ($result) {
                return ResponseHelper::success('Domain verified successfully');
            } else {
                return ResponseHelper::error('Domain verification failed', null, 400);
            }
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Get verification instructions.
     */
    public function getVerificationInstructions(Request $request, string $id): Response
    {
        $request->validate([
            'type' => 'nullable|in:dns,file,meta',
        ]);

        try {
            $domain = $this->domainService->getDomainById($id);
            if (!$domain) {
                return ResponseHelper::error('Domain not found', null, 404);
            }

            $instructions = $this->domainService->getVerificationInstructions(
                $domain,
                $request->get('type', 'dns')
            );

            return ResponseHelper::success(data: $instructions);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Set domain as primary.
     */
    public function setPrimary(string $id): Response
    {
        try {
            $result = $this->domainService->setPrimaryDomain($id);

            if ($result) {
                return ResponseHelper::success('Domain set as primary successfully');
            } else {
                return ResponseHelper::error('Failed to set domain as primary', null, 400);
            }
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }
}
