<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domains', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignUlid('organization_id')->nullable()->constrained('organizations')->cascadeOnDelete();
            
            // Domain information
            $table->string('hostname')->unique(); // e.g., 'app.autopay.vn', 'client1.autopay.vn'
            $table->string('name'); // Display name for the domain
            $table->text('description')->nullable();
            
            // Branding configuration
            $table->string('brand_name')->nullable(); // Custom brand name
            $table->string('slogan')->nullable(); // Brand slogan
            $table->string('logo_url')->nullable(); // Logo image URL
            $table->string('favicon_url')->nullable(); // Favicon URL
            
            // Theme configuration
            $table->json('theme_colors')->nullable(); // Primary, secondary, accent colors
            $table->json('custom_css')->nullable(); // Custom CSS overrides
            $table->string('font_family')->nullable(); // Custom font family
            
            // SEO configuration
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->string('og_image_url')->nullable(); // Open Graph image
            
            // Features configuration
            $table->json('enabled_features')->nullable(); // Array of enabled features
            $table->json('feature_configs')->nullable(); // Feature-specific configurations
            
            // Localization
            $table->string('default_locale')->default('vi');
            $table->json('supported_locales')->nullable(); // Array of supported locales
            
            // Contact information
            $table->json('contact_info')->nullable(); // Phone, email, address, etc.
            
            // Custom configuration
            $table->json('custom_config')->nullable(); // Additional custom configurations
            
            // Status and settings
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false); // Mark as default domain
            $table->boolean('force_https')->default(true);
            $table->string('redirect_to')->nullable(); // Redirect to another domain if needed
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['hostname']);
            $table->index(['organization_id']);
            $table->index(['is_active']);
            $table->index(['is_default']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domains');
    }
};
